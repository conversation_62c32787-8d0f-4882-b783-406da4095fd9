/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.android.internal.ai.IAiContextEngine;
import com.android.internal.ai.IAiPlanningOrchestration;
import com.android.internal.ai.IAiPersonalization;
import com.android.internal.ai.ContextSnapshot;
import com.android.internal.ai.PlanResult;
import com.android.internal.ai.ExecutionResult;

/**
 * Manages connections to Jarvis AI services from SystemUI.
 * 
 * Handles:
 * - Service binding and lifecycle
 * - Message routing to appropriate AI services
 * - Response handling and callbacks
 * - Error handling and recovery
 */
public class JarvisServiceConnection {
    private static final String TAG = "JarvisServiceConnection";
    private static final boolean DEBUG = true;
    
    private final Context mContext;
    private final Handler mMainHandler;
    
    // Service connections
    private IAiContextEngine mContextService;
    private IAiPlanningOrchestration mPlanningService;
    private IAiPersonalization mPersonalizationService;
    
    private boolean mContextServiceConnected = false;
    private boolean mPlanningServiceConnected = false;
    private boolean mPersonalizationServiceConnected = false;
    
    // Callbacks
    private ConnectionListener mConnectionListener;
    
    public interface ConnectionListener {
        void onServiceConnected();
        void onServiceDisconnected();
        void onAiResponse(String response);
        void onError(String error);
    }
    
    public JarvisServiceConnection(Context context) {
        mContext = context;
        mMainHandler = new Handler(Looper.getMainLooper());
    }
    
    public void setConnectionListener(ConnectionListener listener) {
        mConnectionListener = listener;
    }
    
    public void connect() {
        if (DEBUG) Log.d(TAG, "Connecting to AI services");
        
        connectToContextService();
        connectToPlanningService();
        connectToPersonalizationService();
    }
    
    public void disconnect() {
        if (DEBUG) Log.d(TAG, "Disconnecting from AI services");
        
        disconnectFromContextService();
        disconnectFromPlanningService();
        disconnectFromPersonalizationService();
    }
    
    public boolean isConnected() {
        return mContextServiceConnected && mPlanningServiceConnected && mPersonalizationServiceConnected;
    }
    
    public void sendMessage(String message, boolean isVoice) {
        if (!isConnected()) {
            notifyError("AI services not connected");
            return;
        }
        
        mMainHandler.post(() -> processMessage(message, isVoice));
    }
    
    private void connectToContextService() {
        Intent intent = new Intent();
        intent.setComponent(new ComponentName("android", "com.android.server.ai.AiContextEngineService"));
        
        ServiceConnection connection = new ServiceConnection() {
            @Override
            public void onServiceConnected(ComponentName name, IBinder service) {
                mContextService = IAiContextEngine.Stub.asInterface(service);
                mContextServiceConnected = true;
                
                if (DEBUG) Log.d(TAG, "Context service connected");
                checkAllServicesConnected();
            }
            
            @Override
            public void onServiceDisconnected(ComponentName name) {
                mContextService = null;
                mContextServiceConnected = false;
                
                if (DEBUG) Log.d(TAG, "Context service disconnected");
                notifyServiceDisconnected();
            }
        };
        
        try {
            mContext.bindService(intent, connection, Context.BIND_AUTO_CREATE);
        } catch (Exception e) {
            Log.e(TAG, "Failed to bind to context service", e);
        }
    }
    
    private void connectToPlanningService() {
        Intent intent = new Intent();
        intent.setComponent(new ComponentName("android", "com.android.server.ai.AiPlanningOrchestrationService"));
        
        ServiceConnection connection = new ServiceConnection() {
            @Override
            public void onServiceConnected(ComponentName name, IBinder service) {
                mPlanningService = IAiPlanningOrchestration.Stub.asInterface(service);
                mPlanningServiceConnected = true;
                
                if (DEBUG) Log.d(TAG, "Planning service connected");
                checkAllServicesConnected();
            }
            
            @Override
            public void onServiceDisconnected(ComponentName name) {
                mPlanningService = null;
                mPlanningServiceConnected = false;
                
                if (DEBUG) Log.d(TAG, "Planning service disconnected");
                notifyServiceDisconnected();
            }
        };
        
        try {
            mContext.bindService(intent, connection, Context.BIND_AUTO_CREATE);
        } catch (Exception e) {
            Log.e(TAG, "Failed to bind to planning service", e);
        }
    }
    
    private void connectToPersonalizationService() {
        Intent intent = new Intent();
        intent.setComponent(new ComponentName("android", "com.android.server.ai.AiPersonalizationService"));
        
        ServiceConnection connection = new ServiceConnection() {
            @Override
            public void onServiceConnected(ComponentName name, IBinder service) {
                mPersonalizationService = IAiPersonalization.Stub.asInterface(service);
                mPersonalizationServiceConnected = true;
                
                if (DEBUG) Log.d(TAG, "Personalization service connected");
                checkAllServicesConnected();
            }
            
            @Override
            public void onServiceDisconnected(ComponentName name) {
                mPersonalizationService = null;
                mPersonalizationServiceConnected = false;
                
                if (DEBUG) Log.d(TAG, "Personalization service disconnected");
                notifyServiceDisconnected();
            }
        };
        
        try {
            mContext.bindService(intent, connection, Context.BIND_AUTO_CREATE);
        } catch (Exception e) {
            Log.e(TAG, "Failed to bind to personalization service", e);
        }
    }
    
    private void disconnectFromContextService() {
        // Implementation would unbind the service
        mContextService = null;
        mContextServiceConnected = false;
    }
    
    private void disconnectFromPlanningService() {
        // Implementation would unbind the service
        mPlanningService = null;
        mPlanningServiceConnected = false;
    }
    
    private void disconnectFromPersonalizationService() {
        // Implementation would unbind the service
        mPersonalizationService = null;
        mPersonalizationServiceConnected = false;
    }
    
    private void checkAllServicesConnected() {
        if (isConnected() && mConnectionListener != null) {
            mConnectionListener.onServiceConnected();
        }
    }
    
    private void notifyServiceDisconnected() {
        if (mConnectionListener != null) {
            mConnectionListener.onServiceDisconnected();
        }
    }
    
    private void notifyError(String error) {
        if (mConnectionListener != null) {
            mConnectionListener.onError(error);
        }
    }
    
    private void notifyResponse(String response) {
        if (mConnectionListener != null) {
            mConnectionListener.onAiResponse(response);
        }
    }
    
    private void processMessage(String message, boolean isVoice) {
        try {
            // Get current context
            ContextSnapshot context = getCurrentContext();
            
            // Plan the task
            PlanResult planResult = planTask(message, context);
            
            if (planResult != null && planResult.success) {
                // Execute the plan
                ExecutionResult executionResult = executeTask(planResult);
                
                if (executionResult != null && executionResult.success) {
                    notifyResponse(executionResult.result);
                } else {
                    notifyError("Failed to execute task: " + 
                        (executionResult != null ? executionResult.errorMessage : "Unknown error"));
                }
            } else {
                notifyError("Failed to plan task: " + 
                    (planResult != null ? planResult.errorMessage : "Unknown error"));
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing message", e);
            notifyError("Error processing message: " + e.getMessage());
        }
    }
    
    private ContextSnapshot getCurrentContext() throws RemoteException {
        if (mContextService != null) {
            return mContextService.getCurrentContext();
        }
        return null;
    }
    
    private PlanResult planTask(String message, ContextSnapshot context) throws RemoteException {
        if (mPlanningService != null) {
            return mPlanningService.planTask(message, context);
        }
        return null;
    }
    
    private ExecutionResult executeTask(PlanResult planResult) throws RemoteException {
        if (mPlanningService != null && planResult.taskPlan != null) {
            return mPlanningService.executeTask(planResult.taskPlan);
        }
        return null;
    }
    
    // Helper methods for specific AI operations
    public void updateUserPreference(String key, String value) {
        if (mPersonalizationService != null) {
            try {
                Bundle preferences = new Bundle();
                preferences.putString(key, value);
                mPersonalizationService.updatePreferences(preferences);
            } catch (RemoteException e) {
                Log.e(TAG, "Error updating user preference", e);
            }
        }
    }
    
    public void recordUserInteraction(String interactionType, Bundle data) {
        if (mPersonalizationService != null) {
            try {
                mPersonalizationService.recordUserInteraction(interactionType, data);
            } catch (RemoteException e) {
                Log.e(TAG, "Error recording user interaction", e);
            }
        }
    }
    
    public void requestContextUpdate() {
        if (mContextService != null) {
            try {
                // Trigger context refresh
                mContextService.getCurrentContext();
            } catch (RemoteException e) {
                Log.e(TAG, "Error requesting context update", e);
            }
        }
    }
}
