/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.android.systemui.CoreStartable;
import com.android.systemui.dagger.SysUISingleton;
import com.android.systemui.statusbar.CommandQueue;

import javax.inject.Inject;

/**
 * Main controller for Jarvis AI integration in SystemUI.
 * 
 * Manages:
 * - AI service connections
 * - Conversation overlay
 * - Status bar integration
 * - Quick settings integration
 * - Voice activation
 */
@SysUISingleton
public class JarvisSystemUIController implements CoreStartable, 
        JarvisOverlayController.OverlayListener,
        CommandQueue.Callbacks {
    
    private static final String TAG = "JarvisSystemUIController";
    private static final boolean DEBUG = true;
    
    private final Context mContext;
    private final CommandQueue mCommandQueue;
    private final Handler mMainHandler;
    
    // AI Service connections
    private JarvisServiceConnection mServiceConnection;
    
    // UI Components
    private JarvisOverlayController mOverlayController;
    private JarvisStatusBarController mStatusBarController;
    private JarvisQuickSettingsController mQuickSettingsController;
    
    // State
    private boolean mJarvisEnabled = true;
    private boolean mVoiceActivationEnabled = true;
    private boolean mIsProcessingRequest = false;
    
    @Inject
    public JarvisSystemUIController(Context context, CommandQueue commandQueue) {
        mContext = context;
        mCommandQueue = commandQueue;
        mMainHandler = new Handler(Looper.getMainLooper());
    }
    
    @Override
    public void start() {
        if (DEBUG) Log.d(TAG, "Starting Jarvis SystemUI Controller");
        
        // Initialize AI service connection
        initializeServiceConnection();
        
        // Initialize UI controllers
        initializeUIControllers();
        
        // Register with CommandQueue for system callbacks
        mCommandQueue.addCallback(this);
        
        if (DEBUG) Log.d(TAG, "Jarvis SystemUI Controller started");
    }
    
    private void initializeServiceConnection() {
        mServiceConnection = new JarvisServiceConnection(mContext);
        mServiceConnection.setConnectionListener(new JarvisServiceConnection.ConnectionListener() {
            @Override
            public void onServiceConnected() {
                if (DEBUG) Log.d(TAG, "AI services connected");
                updateJarvisAvailability(true);
            }
            
            @Override
            public void onServiceDisconnected() {
                if (DEBUG) Log.d(TAG, "AI services disconnected");
                updateJarvisAvailability(false);
            }
            
            @Override
            public void onAiResponse(String response) {
                handleAiResponse(response);
            }
            
            @Override
            public void onError(String error) {
                handleAiError(error);
            }
        });
        
        mServiceConnection.connect();
    }
    
    private void initializeUIControllers() {
        // Initialize overlay controller
        mOverlayController = new JarvisOverlayController(mContext);
        mOverlayController.setOverlayListener(this);
        
        // Initialize status bar controller
        mStatusBarController = new JarvisStatusBarController(mContext);
        mStatusBarController.setStatusBarListener(new JarvisStatusBarController.StatusBarListener() {
            @Override
            public void onJarvisIconClicked() {
                showJarvisOverlay();
            }
            
            @Override
            public void onVoiceActivationTriggered() {
                if (mVoiceActivationEnabled) {
                    showJarvisOverlay();
                }
            }
        });
        
        // Initialize quick settings controller
        mQuickSettingsController = new JarvisQuickSettingsController(mContext);
        mQuickSettingsController.setQuickSettingsListener(new JarvisQuickSettingsController.QuickSettingsListener() {
            @Override
            public void onJarvisToggled(boolean enabled) {
                setJarvisEnabled(enabled);
            }
            
            @Override
            public void onVoiceActivationToggled(boolean enabled) {
                setVoiceActivationEnabled(enabled);
            }
            
            @Override
            public void onJarvisSettingsRequested() {
                openJarvisSettings();
            }
        });
    }
    
    public void showJarvisOverlay() {
        if (!mJarvisEnabled) {
            if (DEBUG) Log.d(TAG, "Jarvis is disabled, not showing overlay");
            return;
        }
        
        if (mOverlayController != null) {
            mOverlayController.showOverlay();
        }
    }
    
    public void hideJarvisOverlay() {
        if (mOverlayController != null) {
            mOverlayController.hideOverlay();
        }
    }
    
    public void setJarvisEnabled(boolean enabled) {
        mJarvisEnabled = enabled;
        
        // Update UI controllers
        if (mStatusBarController != null) {
            mStatusBarController.setJarvisEnabled(enabled);
        }
        
        if (mQuickSettingsController != null) {
            mQuickSettingsController.setJarvisEnabled(enabled);
        }
        
        // Hide overlay if disabled
        if (!enabled && mOverlayController != null && mOverlayController.isShowing()) {
            mOverlayController.hideOverlay();
        }
        
        if (DEBUG) Log.d(TAG, "Jarvis enabled: " + enabled);
    }
    
    public void setVoiceActivationEnabled(boolean enabled) {
        mVoiceActivationEnabled = enabled;
        
        if (mStatusBarController != null) {
            mStatusBarController.setVoiceActivationEnabled(enabled);
        }
        
        if (DEBUG) Log.d(TAG, "Voice activation enabled: " + enabled);
    }
    
    private void updateJarvisAvailability(boolean available) {
        if (mStatusBarController != null) {
            mStatusBarController.setJarvisAvailable(available);
        }
        
        if (mQuickSettingsController != null) {
            mQuickSettingsController.setJarvisAvailable(available);
        }
        
        if (!available && mOverlayController != null && mOverlayController.isShowing()) {
            mOverlayController.hideOverlay();
        }
    }
    
    private void handleAiResponse(String response) {
        mIsProcessingRequest = false;
        
        if (mOverlayController != null) {
            mOverlayController.addAiResponse(response);
            mOverlayController.setProcessingState(false);
        }
        
        if (DEBUG) Log.d(TAG, "AI response received: " + response);
    }
    
    private void handleAiError(String error) {
        mIsProcessingRequest = false;
        
        if (mOverlayController != null) {
            mOverlayController.addAiResponse("Sorry, I encountered an error: " + error);
            mOverlayController.setProcessingState(false);
        }
        
        Log.e(TAG, "AI error: " + error);
    }
    
    private void openJarvisSettings() {
        Intent intent = new Intent("android.settings.AI_ASSISTANT_SETTINGS");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        
        try {
            mContext.startActivity(intent);
        } catch (Exception e) {
            Log.w(TAG, "Could not open Jarvis settings", e);
        }
    }
    
    // JarvisOverlayController.OverlayListener implementation
    @Override
    public void onOverlayShown() {
        if (mStatusBarController != null) {
            mStatusBarController.setOverlayVisible(true);
        }
        
        if (DEBUG) Log.d(TAG, "Jarvis overlay shown");
    }
    
    @Override
    public void onOverlayHidden() {
        if (mStatusBarController != null) {
            mStatusBarController.setOverlayVisible(false);
        }
        
        if (DEBUG) Log.d(TAG, "Jarvis overlay hidden");
    }
    
    @Override
    public void onMessageSent(String message, boolean isVoice) {
        if (mServiceConnection == null || !mServiceConnection.isConnected()) {
            handleAiError("AI services not available");
            return;
        }
        
        mIsProcessingRequest = true;
        
        if (mOverlayController != null) {
            mOverlayController.setProcessingState(true);
        }
        
        // Send message to AI services
        mServiceConnection.sendMessage(message, isVoice);
        
        if (DEBUG) Log.d(TAG, "Message sent to AI: " + message + " (voice: " + isVoice + ")");
    }
    
    @Override
    public void onQuickActionTriggered(String actionId) {
        if (mServiceConnection == null || !mServiceConnection.isConnected()) {
            handleAiError("AI services not available");
            return;
        }
        
        // Handle quick actions
        switch (actionId) {
            case "help":
                onMessageSent("What can you do?", false);
                break;
            case "open_settings":
                openJarvisSettings();
                break;
            case "check_weather":
                onMessageSent("What's the weather like?", false);
                break;
            case "set_reminder":
                onMessageSent("Set a reminder", false);
                break;
            default:
                if (DEBUG) Log.d(TAG, "Unknown quick action: " + actionId);
                break;
        }
    }
    
    // CommandQueue.Callbacks implementation
    @Override
    public void onRecentsAnimationStateChanged(boolean running) {
        // Hide Jarvis overlay when recents animation starts
        if (running && mOverlayController != null && mOverlayController.isShowing()) {
            mOverlayController.hideOverlay();
        }
    }
    
    // Public API for other SystemUI components
    public boolean isJarvisEnabled() {
        return mJarvisEnabled;
    }
    
    public boolean isVoiceActivationEnabled() {
        return mVoiceActivationEnabled;
    }
    
    public boolean isJarvisAvailable() {
        return mServiceConnection != null && mServiceConnection.isConnected();
    }
    
    public boolean isOverlayShowing() {
        return mOverlayController != null && mOverlayController.isShowing();
    }
    
    public void triggerVoiceActivation() {
        if (mVoiceActivationEnabled && mJarvisEnabled) {
            showJarvisOverlay();
        }
    }
    
    @Override
    public void dump(java.io.PrintWriter pw, String[] args) {
        pw.println("JarvisSystemUIController state:");
        pw.println("  mJarvisEnabled=" + mJarvisEnabled);
        pw.println("  mVoiceActivationEnabled=" + mVoiceActivationEnabled);
        pw.println("  mIsProcessingRequest=" + mIsProcessingRequest);
        pw.println("  isJarvisAvailable=" + isJarvisAvailable());
        pw.println("  isOverlayShowing=" + isOverlayShowing());
    }
}
